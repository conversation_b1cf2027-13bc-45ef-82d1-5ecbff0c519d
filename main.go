// +build !gl

package main

import (
	"os"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

func main() {
	// 设置软件渲染模式，避免OpenGL依赖
	os.Setenv("FYNE_DRIVER", "software")

	myApp := app.New()
	myWindow := myApp.NewWindow("简单GUI程序")
	myWindow.Resize(fyne.NewSize(400, 300))

	hello := widget.NewLabel("欢迎使用Fyne GUI程序!")
	hello.Alignment = fyne.TextAlignCenter

	entry := widget.NewEntry()
	entry.SetPlaceHolder("请输入文本...")

	button := widget.NewButton("点击我", func() {
		if entry.Text != "" {
			hello.SetText("你输入了: " + entry.Text)
		} else {
			hello.SetText("请先输入一些文本!")
		}
	})

	clearButton := widget.NewButton("清空", func() {
		entry.SetText("")
		hello.SetText("欢迎使用Fyne GUI程序!")
	})

	content := container.NewVBox(
		hello,
		widget.NewSeparator(),
		entry,
		container.NewHBox(button, clearButton),
	)

	myWindow.SetContent(content)
	myWindow.ShowAndRun()
}
